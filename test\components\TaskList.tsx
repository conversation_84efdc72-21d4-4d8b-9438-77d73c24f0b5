import React from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  Pressable
} from 'react-native';
import { Task } from '../types';
import { TaskItem } from './TaskItem';
import { useTasks } from '../hooks/useTasks';
import { commonStyles } from '../styles';

interface TaskListProps {
  tasks?: Task[]; // Optional - will use API if not provided
  onToggleTask?: (taskId: string) => void; // Optional - will use API if not provided
}

export const TaskList: React.FC<TaskListProps> = ({
  tasks: propTasks,
  onToggleTask: propOnToggleTask
}) => {
  const {
    tasks: apiTasks,
    loading,
    error,
    toggleTaskCompletion,
    deleteTask,
    loadTasks
  } = useTasks();

  // Use prop tasks if provided, otherwise use API tasks
  const tasks = propTasks || apiTasks;
  const onToggleTask = propOnToggleTask || toggleTaskCompletion;

  const handleToggle = async (taskId: string) => {
    try {
      await onToggleTask(taskId);
    } catch (error) {
      window.alert('Failed to update task');
    }
  };

  const handleDelete = async (taskId: string) => {
    console.log('handleDelete called with taskId:', taskId);

    // Use browser confirm dialog for web compatibility
    const confirmed = window.confirm('Are you sure you want to delete this task?');

    if (confirmed) {
      console.log('Delete confirmed for taskId:', taskId);
      try {
        await deleteTask(taskId);
        console.log('Delete successful for taskId:', taskId);
      } catch (error) {
        console.error('Delete failed for taskId:', taskId, error);
        window.alert('Failed to delete task');
      }
    } else {
      console.log('Delete cancelled for taskId:', taskId);
    }
  };

  if (loading && tasks.length === 0) {
    return (
      <View style={commonStyles.section}>
        <Text style={commonStyles.sectionTitle}>📋 To-Do List</Text>
        <View style={{ alignItems: 'center', padding: 20 }}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={{ marginTop: 10, color: '#666' }}>Loading tasks...</Text>
        </View>
      </View>
    );
  }

  if (error && !propTasks) {
    return (
      <View style={commonStyles.section}>
        <Text style={commonStyles.sectionTitle}>📋 To-Do List</Text>
        <View style={{ alignItems: 'center', padding: 20 }}>
          <Text style={{ color: 'red', marginBottom: 10 }}>Error: {error}</Text>
          <Pressable onPress={loadTasks} style={{ backgroundColor: '#007AFF', padding: 10, borderRadius: 8 }}>
            <Text style={{ color: 'white' }}>Retry</Text>
          </Pressable>
        </View>
      </View>
    );
  }

  return (
    <View style={commonStyles.section}>
      <Text style={commonStyles.sectionTitle}>📋 To-Do List</Text>
      {tasks.length === 0 ? (
        <Text style={{ color: '#666', textAlign: 'center', padding: 20 }}>No tasks yet</Text>
      ) : (
        tasks.map((task) => (
          <TaskItem
            key={task.task_id || task.id}
            task={task}
            onToggle={() => handleToggle(task.task_id || task.id || '')}
            onDelete={() => handleDelete(task.task_id || task.id || '')}
          />
        ))
      )}
    </View>
  );
};
